/**
 * Individual staff user management endpoint for admin server
 * Handles GET, PUT, DELETE operations for specific staff users
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, hashPassword } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  validateRequiredFields,
  isValidEmail,
  isValidUUID
} from '@/lib/utils';
import { logUserOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/types';

interface User {
  id: string;
  email: string;
  role: UserRole;
  name: string;
  is_active: boolean;
  server_type: string;
  created_at: Date;
  updated_at: Date;
}

// GET /api/staff-integration/users/[id] - Get specific staff user
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate admin user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only admin users can view staff users
    if (authResult.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    try {
      const userResult = await query<User>(
        `SELECT id, email, role, name, is_active, server_type, created_at, updated_at
         FROM users 
         WHERE id = $1 AND server_type = 'staff'`,
        [id]
      );

      if (userResult.rows.length === 0) {
        return createErrorResponse('Staff user not found', 404);
      }

      return createResponse(userResult.rows[0], true, 'Staff user retrieved successfully');

    } catch (dbError) {
      console.error('Database error getting staff user:', dbError);
      return createErrorResponse('Failed to retrieve staff user', 500);
    }

  } catch (error) {
    console.error('Get staff user error:', error);
    return createErrorResponse('Failed to retrieve staff user', 500);
  }
}

// PUT /api/staff-integration/users/[id] - Update specific staff user
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate admin user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only admin users can update staff users
    if (authResult.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    const body = await request.json();
    const { email, password, role, name, isActive } = body;

    // Validate email format if provided
    if (email && !isValidEmail(email)) {
      return createErrorResponse('Invalid email format', 400);
    }

    // Validate staff role if provided
    if (role && !['management', 'reception', 'teacher'].includes(role)) {
      return createErrorResponse('Invalid role. Must be management, reception, or teacher', 400);
    }

    const context = getRequestContext(request);

    try {
      // Get current user data
      const currentUserResult = await query<User>(
        `SELECT id, email, role, name, is_active, server_type, created_at, updated_at
         FROM users 
         WHERE id = $1 AND server_type = 'staff'`,
        [id]
      );

      if (currentUserResult.rows.length === 0) {
        return createErrorResponse('Staff user not found', 404);
      }

      const currentUser = currentUserResult.rows[0];

      // Check for email conflicts if email is being changed
      if (email && email.toLowerCase() !== currentUser.email) {
        const emailCheckResult = await query(
          'SELECT id FROM users WHERE email = $1 AND id != $2',
          [email.toLowerCase(), id]
        );

        if (emailCheckResult.rows.length > 0) {
          return createErrorResponse('Email already exists', 409);
        }
      }

      // Build update query dynamically
      const updates: string[] = [];
      const values: any[] = [];
      let paramIndex = 1;

      if (email !== undefined) {
        updates.push(`email = $${paramIndex++}`);
        values.push(email.toLowerCase());
      }

      if (password !== undefined && password.trim() !== '') {
        const passwordHash = await hashPassword(password);
        updates.push(`password_hash = $${paramIndex++}`);
        values.push(passwordHash);
      }

      if (role !== undefined) {
        updates.push(`role = $${paramIndex++}`);
        values.push(role);
      }

      if (name !== undefined) {
        updates.push(`name = $${paramIndex++}`);
        values.push(name);
      }

      if (isActive !== undefined) {
        updates.push(`is_active = $${paramIndex++}`);
        values.push(isActive);
      }

      if (updates.length === 0) {
        return createErrorResponse('No valid fields to update', 400);
      }

      updates.push(`updated_at = CURRENT_TIMESTAMP`);
      values.push(id);

      const updateQuery = `
        UPDATE users 
        SET ${updates.join(', ')}
        WHERE id = $${paramIndex} AND server_type = 'staff'
        RETURNING id, email, role, name, is_active, server_type, created_at, updated_at
      `;

      const updateResult = await query<User>(updateQuery, values);

      if (updateResult.rows.length === 0) {
        return createErrorResponse('Staff user not found or update failed', 404);
      }

      const updatedUser = updateResult.rows[0];

      // Log user update
      await logUserOperation(
        'UPDATE' as any,
        authResult.user.id,
        updatedUser,
        currentUser,
        context
      );

      return createResponse(updatedUser, true, 'Staff user updated successfully');

    } catch (dbError) {
      console.error('Database error updating staff user:', dbError);
      
      if (dbError instanceof Error && dbError.message.includes('duplicate key')) {
        return createErrorResponse('Email already exists', 409);
      }
      
      return createErrorResponse('Failed to update staff user', 500);
    }

  } catch (error) {
    console.error('Update staff user error:', error);
    return createErrorResponse('Failed to update staff user', 500);
  }
}

// DELETE /api/staff-integration/users/[id] - Deactivate specific staff user
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate admin user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Only admin users can deactivate staff users
    if (authResult.user.role !== UserRole.ADMIN) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { id } = await params;

    // Validate UUID format
    if (!isValidUUID(id)) {
      return createErrorResponse('Invalid user ID format', 400);
    }

    const context = getRequestContext(request);

    try {
      // Get current user data before deactivation
      const currentUserResult = await query<User>(
        `SELECT id, email, role, name, is_active, server_type, created_at, updated_at
         FROM users 
         WHERE id = $1 AND server_type = 'staff'`,
        [id]
      );

      if (currentUserResult.rows.length === 0) {
        return createErrorResponse('Staff user not found', 404);
      }

      const currentUser = currentUserResult.rows[0];

      // Deactivate user (soft delete)
      const deactivateResult = await query<User>(
        `UPDATE users 
         SET is_active = false, updated_at = CURRENT_TIMESTAMP
         WHERE id = $1 AND server_type = 'staff'
         RETURNING id, email, role, name, is_active, server_type, created_at, updated_at`,
        [id]
      );

      if (deactivateResult.rows.length === 0) {
        return createErrorResponse('Staff user not found or deactivation failed', 404);
      }

      const deactivatedUser = deactivateResult.rows[0];

      // Log user deactivation
      await logUserOperation(
        'UPDATE' as any,
        authResult.user.id,
        deactivatedUser,
        currentUser,
        context
      );

      return createResponse(deactivatedUser, true, 'Staff user deactivated successfully');

    } catch (dbError) {
      console.error('Database error deactivating staff user:', dbError);
      return createErrorResponse('Failed to deactivate staff user', 500);
    }

  } catch (error) {
    console.error('Deactivate staff user error:', error);
    return createErrorResponse('Failed to deactivate staff user', 500);
  }
}
