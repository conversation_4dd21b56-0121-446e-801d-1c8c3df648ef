/**
 * Group/Class management endpoints
 * Handles CRUD operations for group management, teacher assignment, and scheduling
 */

import { NextRequest } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';
import { 
  createResponse, 
  createErrorResponse, 
  parsePaginationParams, 
  parseFilterParams,
  validateRequiredFields,
  isValidUUID
} from '@/lib/utils';
import { logGroupOperation, getRequestContext } from '@/lib/activity-logger';
import { UserRole } from '@/shared/types/common';
import { buildWhereClause } from '@/lib/db';

interface Group {
  id: string;
  name: string;
  level?: string;
  teacher_id?: string;
  max_students: number;
  schedule?: any;
  is_active: boolean;
  created_at: Date;
  updated_at: Date;
}

interface GroupWithTeacher extends Group {
  teacher_name?: string;
  teacher_email?: string;
  current_students?: number;
}

// GET /api/groups - List groups with pagination and filtering
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - all staff roles can view groups
    if (!['management', 'reception', 'teacher'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const { searchParams } = new URL(request.url);
    const pagination = parsePaginationParams(searchParams);
    const filters = parseFilterParams(searchParams);

    try {
      // Build WHERE clause for filtering
      const conditions: Record<string, any> = {};
      let paramIndex = 1;

      // Handle search across multiple fields
      if (filters.search) {
        const searchTerm = `%${filters.search}%`;
        const searchResult = await query<{total: string}>(
          `SELECT COUNT(*) as total FROM groups g
           LEFT JOIN users u ON g.teacher_id = u.id
           WHERE (g.name ILIKE $1 OR g.level ILIKE $1 OR u.name ILIKE $1)`,
          [searchTerm]
        );
        const total = parseInt(searchResult.rows[0].total);

        const offset = (pagination.page - 1) * pagination.limit;
        const groupsResult = await query<GroupWithTeacher>(
          `SELECT g.id, g.name, g.level, g.teacher_id, g.max_students, 
                  g.schedule, g.is_active, g.created_at, g.updated_at,
                  u.name as teacher_name, u.email as teacher_email,
                  (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id) as current_students
           FROM groups g
           LEFT JOIN users u ON g.teacher_id = u.id
           WHERE (g.name ILIKE $1 OR g.level ILIKE $1 OR u.name ILIKE $1)
           ORDER BY g.created_at DESC 
           LIMIT $2 OFFSET $3`,
          [searchTerm, pagination.limit, offset]
        );

        return createResponse({
          groups: groupsResult.rows,
          pagination: {
            page: pagination.page,
            limit: pagination.limit,
            total,
            totalPages: Math.ceil(total / pagination.limit),
            hasNext: pagination.page < Math.ceil(total / pagination.limit),
            hasPrev: pagination.page > 1
          }
        }, true, 'Groups retrieved successfully');
      }

      // Add teacher filter
      if (filters.teacherId && isValidUUID(filters.teacherId)) {
        conditions['g.teacher_id'] = filters.teacherId;
      }

      // Add level filter
      if (filters.level) {
        conditions['g.level'] = filters.level;
      }

      // Add active status filter
      if (filters.isActive !== undefined) {
        conditions['g.is_active'] = filters.isActive === 'true';
      }

      const { whereClause, params, nextIndex } = buildWhereClause(conditions, paramIndex);
      paramIndex = nextIndex;

      // Get total count
      const countSql = `SELECT COUNT(*) as total FROM groups g ${whereClause}`;
      const countResult = await query(countSql, params);
      const total = parseInt(countResult.rows[0].total);

      // Get paginated results with teacher information and student count
      const offset = (pagination.page - 1) * pagination.limit;
      const dataSql = `
        SELECT g.id, g.name, g.level, g.teacher_id, g.max_students, 
               g.schedule, g.is_active, g.created_at, g.updated_at,
               u.name as teacher_name, u.email as teacher_email,
               (SELECT COUNT(*) FROM student_groups sg WHERE sg.group_id = g.id) as current_students
        FROM groups g
        LEFT JOIN users u ON g.teacher_id = u.id
        ${whereClause} 
        ORDER BY g.created_at DESC 
        LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
      `;

      const groupsResult = await query<GroupWithTeacher>(dataSql, [...params, pagination.limit, offset]);

      return createResponse({
        groups: groupsResult.rows,
        pagination: {
          page: pagination.page,
          limit: pagination.limit,
          total,
          totalPages: Math.ceil(total / pagination.limit),
          hasNext: pagination.page < Math.ceil(total / pagination.limit),
          hasPrev: pagination.page > 1
        }
      }, true, 'Groups retrieved successfully');

    } catch (dbError) {
      console.error('Database error retrieving groups:', dbError);
      return createErrorResponse('Failed to retrieve groups', 500);
    }

  } catch (error) {
    console.error('Get groups error:', error);
    return createErrorResponse('Failed to retrieve groups', 500);
  }
}

// POST /api/groups - Create new group
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getUserFromRequest(request);
    if (!authResult.success || !authResult.user) {
      return createErrorResponse('Authentication required', 401);
    }

    // Check permissions - management and reception can create groups
    if (!['management', 'reception'].includes(authResult.user.role)) {
      return createErrorResponse('Insufficient permissions', 403);
    }

    const body = await request.json();
    const { 
      name, 
      level, 
      teacherId, 
      maxStudents = 15,
      schedule,
      isActive = true
    } = body;

    // Validate required fields
    const validation = validateRequiredFields(body, ['name']);
    if (!validation.isValid) {
      return createErrorResponse(
        `Missing required fields: ${validation.missingFields.join(', ')}`,
        400
      );
    }

    // Validate teacherId if provided
    if (teacherId && !isValidUUID(teacherId)) {
      return createErrorResponse('Invalid teacher ID format', 400);
    }

    // Validate maxStudents
    if (maxStudents < 1 || maxStudents > 50) {
      return createErrorResponse('Max students must be between 1 and 50', 400);
    }

    // Validate schedule format if provided
    if (schedule && typeof schedule !== 'object') {
      return createErrorResponse('Schedule must be a valid JSON object', 400);
    }

    const context = getRequestContext(request.headers);

    try {
      // Check if teacher exists and is a teacher
      if (teacherId) {
        const teacherResult = await query(
          'SELECT id, role FROM users WHERE id = $1 AND is_active = true',
          [teacherId]
        );

        if (teacherResult.rows.length === 0) {
          return createErrorResponse('Teacher not found or inactive', 400);
        }

        const teacher = teacherResult.rows[0];
        if (teacher.role !== 'teacher') {
          return createErrorResponse('Assigned user must be a teacher', 400);
        }
      }

      // Check if group with same name already exists
      const existingGroupResult = await query(
        'SELECT id FROM groups WHERE name = $1',
        [name.trim()]
      );

      if (existingGroupResult.rows.length > 0) {
        return createErrorResponse('Group with this name already exists', 409);
      }

      // Create group
      const groupResult = await query<Group>(
        `INSERT INTO groups (name, level, teacher_id, max_students, schedule, is_active)
         VALUES ($1, $2, $3, $4, $5, $6)
         RETURNING id, name, level, teacher_id, max_students, schedule, is_active, created_at, updated_at`,
        [
          name.trim(),
          level?.trim() || null,
          teacherId || null,
          maxStudents,
          schedule ? JSON.stringify(schedule) : null,
          isActive
        ]
      );

      const newGroup = groupResult.rows[0];

      // Get teacher information if assigned
      let teacherInfo = null;
      if (newGroup.teacher_id) {
        const teacherResult = await query(
          'SELECT name, email FROM users WHERE id = $1',
          [newGroup.teacher_id]
        );
        if (teacherResult.rows.length > 0) {
          teacherInfo = teacherResult.rows[0];
        }
      }

      // Log group creation
      await logGroupOperation(
        'CREATE' as any,
        authResult.user.id,
        newGroup,
        undefined,
        context
      );

      return createResponse({
        id: newGroup.id,
        name: newGroup.name,
        level: newGroup.level,
        teacherId: newGroup.teacher_id,
        teacherName: teacherInfo?.name,
        teacherEmail: teacherInfo?.email,
        maxStudents: newGroup.max_students,
        schedule: newGroup.schedule,
        isActive: newGroup.is_active,
        currentStudents: 0,
        createdAt: newGroup.created_at,
        updatedAt: newGroup.updated_at
      }, true, 'Group created successfully', undefined, 201);

    } catch (dbError) {
      console.error('Database error creating group:', dbError);
      return createErrorResponse('Failed to create group', 500);
    }

  } catch (error) {
    console.error('Create group error:', error);
    return createErrorResponse('Invalid request format', 400);
  }
}
